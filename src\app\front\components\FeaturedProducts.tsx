'use client';

import React from 'react';
import { Card, Row, Col, Badge } from 'antd';
import { EyeOutlined, HeartOutlined, ShoppingCartOutlined } from '@ant-design/icons';
import Link from 'next/link';

const { Meta } = Card;

/**
 * 精品定制产品展示组件
 */
export default function FeaturedProducts() {
  // 精品定制产品数据
  const featuredProducts = [
    {
      id: 1,
      name: '屋脊样式',
      image: '/images/products/wjys.jpg',
      popularity: 23267,
      description: '经典屋脊造型，适用于各种产品包装',
      link: '/front/products/wjys',
      isHot: true,
    },
    {
      id: 2,
      name: '大翻盖样式',
      image: '/images/products/dfgys.jpg',
      popularity: 16558,
      description: '大翻盖设计，方便取放物品',
      link: '/front/products/dfgys',
      isNew: true,
    },
    {
      id: 3,
      name: '自提样式',
      image: '/images/products/ztys.jpg',
      popularity: 3313,
      description: '便携自提设计，外出携带方便',
      link: '/front/products/ztys',
    },
    {
      id: 4,
      name: '飞机盒样式',
      image: '/images/products/fjhys.jpg',
      popularity: 4982,
      description: '飞机盒造型，快递包装首选',
      link: '/front/products/fjhys',
    },
    {
      id: 5,
      name: '手提礼盒样式',
      image: '/images/products/stlhys.jpg',
      popularity: 573,
      description: '精美手提礼盒，送礼首选',
      link: '/front/products/stlhys',
      isHot: true,
    },
    {
      id: 6,
      name: '手提袋样式',
      image: '/images/products/stdys.jpg',
      popularity: 10019,
      description: '环保手提袋，绿色包装选择',
      link: '/front/products/stdys',
    },
  ];

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题区域 */}
        <div className="flex justify-between items-center mb-12">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              精品定制
            </h2>
            <p className="text-lg text-gray-600">
              专业设计团队为您量身定制，满足个性化需求
            </p>
          </div>
          <Link href="/front/products">
            <button className="hidden md:block bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              更多+
            </button>
          </Link>
        </div>

        {/* 产品网格 */}
        <Row gutter={[24, 24]}>
          {featuredProducts.map((product) => (
            <Col xs={12} sm={8} md={6} lg={4} key={product.id}>
              <Badge.Ribbon 
                text={product.isHot ? "热门" : product.isNew ? "新品" : ""} 
                color={product.isHot ? "red" : product.isNew ? "green" : ""}
                style={{ display: product.isHot || product.isNew ? 'block' : 'none' }}
              >
                <Card
                  hoverable
                  className="h-full"
                  cover={
                    <div className="relative overflow-hidden h-48 bg-gray-100">
                      {/* 占位图片区域 */}
                      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-50 to-blue-100">
                        <div className="text-center">
                          <div className="w-16 h-16 bg-blue-200 rounded-lg mx-auto mb-2 flex items-center justify-center">
                            <span className="text-blue-600 text-2xl font-bold">
                              {product.name.charAt(0)}
                            </span>
                          </div>
                          <span className="text-blue-600 text-sm">{product.name}</span>
                        </div>
                      </div>
                      
                      {/* 悬浮操作按钮 */}
                      <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center opacity-0 hover:opacity-100">
                        <div className="flex space-x-2">
                          <button className="w-10 h-10 bg-white rounded-full flex items-center justify-center text-gray-600 hover:text-blue-600 transition-colors">
                            <EyeOutlined />
                          </button>
                          <button className="w-10 h-10 bg-white rounded-full flex items-center justify-center text-gray-600 hover:text-red-600 transition-colors">
                            <HeartOutlined />
                          </button>
                          <button className="w-10 h-10 bg-white rounded-full flex items-center justify-center text-gray-600 hover:text-green-600 transition-colors">
                            <ShoppingCartOutlined />
                          </button>
                        </div>
                      </div>
                    </div>
                  }
                  actions={[
                    <Link key="view" href={product.link}>
                      <span className="text-blue-600 hover:text-blue-800">查看详情</span>
                    </Link>,
                    <Link key="quote" href="/front/quote">
                      <span className="text-green-600 hover:text-green-800">立即报价</span>
                    </Link>,
                  ]}
                >
                  <Meta
                    title={
                      <Link href={product.link}>
                        <span className="text-gray-900 hover:text-blue-600 transition-colors">
                          {product.name}
                        </span>
                      </Link>
                    }
                    description={
                      <div>
                        <p className="text-gray-600 text-sm mb-2">{product.description}</p>
                        <p className="text-gray-500 text-xs">
                          人气 {product.popularity.toLocaleString()}
                        </p>
                      </div>
                    }
                  />
                </Card>
              </Badge.Ribbon>
            </Col>
          ))}
        </Row>

        {/* 移动端更多按钮 */}
        <div className="text-center mt-8 md:hidden">
          <Link href="/front/products">
            <button className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors">
              查看更多产品
            </button>
          </Link>
        </div>
      </div>
    </section>
  );
}
