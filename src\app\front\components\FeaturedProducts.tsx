'use client';

import React from 'react';
import Link from 'next/link';

/**
 * 精品定制产品展示组件 - 仿照参考网站设计
 */
export default function FeaturedProducts() {
  // 精品定制产品数据
  const featuredProducts = [
    {
      id: 1,
      name: '屋脊样式',
      image: '/images/products/wjys.jpg',
      popularity: 23267,
      link: '/front/products/wjys',
    },
    {
      id: 2,
      name: '大翻盖样式',
      image: '/images/products/dfgys.jpg',
      popularity: 16558,
      link: '/front/products/dfgys',
    },
    {
      id: 3,
      name: '自提样式',
      image: '/images/products/ztys.jpg',
      popularity: 3313,
      link: '/front/products/ztys',
    },
    {
      id: 4,
      name: '飞机盒样式',
      image: '/images/products/fjhys.jpg',
      popularity: 4982,
      link: '/front/products/fjhys',
    },
    {
      id: 5,
      name: '手提礼盒样式',
      image: '/images/products/stlhys.jpg',
      popularity: 573,
      link: '/front/products/stlhys',
    },
    {
      id: 6,
      name: '手提袋样式',
      image: '/images/products/stdys.jpg',
      popularity: 10019,
      link: '/front/products/stdys',
    },
  ];

  return (
    <div style={{ backgroundColor: '#fff', border: '1px solid #e0e0e0', marginBottom: '10px' }}>
      {/* 标题区域 */}
      <div
        style={{
          backgroundColor: '#f5f5f5',
          padding: '10px 15px',
          borderBottom: '1px solid #e0e0e0',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}
      >
        <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#333' }}>
          精品定制
        </span>
        <Link
          href="/front/products"
          style={{ fontSize: '12px', color: '#1890ff', textDecoration: 'none' }}
        >
          更多+
        </Link>
      </div>

      {/* 产品网格 */}
      <div style={{ padding: '15px' }}>
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(3, 1fr)',
            gap: '10px'
          }}
        >
          {featuredProducts.map((product) => (
            <div key={product.id} style={{ textAlign: 'center' }}>
              <Link href={product.link} style={{ textDecoration: 'none' }}>
                <div
                  style={{
                    border: '1px solid #f0f0f0',
                    padding: '10px',
                    backgroundColor: '#fafafa',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = '#1890ff';
                    e.currentTarget.style.backgroundColor = '#f0f8ff';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = '#f0f0f0';
                    e.currentTarget.style.backgroundColor = '#fafafa';
                  }}
                >
                  {/* 产品图片 */}
                  <div style={{ height: '80px', marginBottom: '8px', overflow: 'hidden' }}>
                    <img
                      src={product.image}
                      alt={product.name}
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                        display: 'block'
                      }}
                      onError={(e) => {
                        // 如果图片加载失败，显示占位符
                        const target = e.target as HTMLImageElement;
                        target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjgwIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9IiNkZGQiLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1zaXplPSIxMiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iIGZpbGw9IiM5OTkiPuWbvueJhzwvdGV4dD48L3N2Zz4=';
                      }}
                    />
                  </div>

                  {/* 产品名称 */}
                  <div style={{ fontSize: '12px', color: '#333', marginBottom: '4px' }}>
                    {product.name}
                  </div>

                  {/* 人气 */}
                  <div style={{ fontSize: '11px', color: '#999' }}>
                    人气 {product.popularity.toLocaleString()}
                  </div>
                </div>
              </Link>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
