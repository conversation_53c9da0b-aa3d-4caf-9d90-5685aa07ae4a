'use client';

import React from 'react';
import Link from 'next/link';

/**
 * 页脚组件 - 仿照参考网站设计
 */
export default function Footer() {
  // 服务指南链接
  const serviceGuides = [
    { name: '服务条款', link: '/front/service/terms' },
    { name: '工艺介绍', link: '/front/service/process' },
    { name: '货期及验货标准', link: '/front/service/delivery' },
    { name: '下单注意事项', link: '/front/service/order-notes' },
    { name: '常见问题', link: '/front/service/faq' },
  ];

  // 支付方式链接
  const paymentMethods = [
    { name: '在线支付', link: '/front/payment/online' },
    { name: '银行汇款', link: '/front/payment/bank' },
    { name: '发票说明', link: '/front/payment/invoice' },
  ];

  // 配送方式链接
  const deliveryMethods = [
    { name: '配送范围及时间', link: '/front/delivery/scope' },
    { name: '产品签收与验货', link: '/front/delivery/inspection' },
    { name: '配送费用', link: '/front/delivery/cost' },
  ];

  // 售后服务链接
  const afterSales = [
    { name: '售后服务说明', link: '/front/service/after-sales' },
    { name: '返工退换货', link: '/front/service/return' },
    { name: '退款说明', link: '/front/service/refund' },
  ];

  // 关于我们链接
  const aboutUs = [
    { name: '公司简介', link: '/front/about/company' },
    { name: '人才招聘', link: '/front/about/careers' },
    { name: '联系我们', link: '/front/about/contact' },
  ];

  return (
    <footer style={{ backgroundColor: '#333', color: '#ccc', marginTop: '20px' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '20px' }}>
        {/* 主要内容区域 */}
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(5, 1fr)', gap: '30px', marginBottom: '20px' }}>
          {/* 买家指南 */}
          <div>
            <h3 style={{ color: '#fff', fontSize: '14px', fontWeight: 'bold', marginBottom: '10px' }}>买家指南</h3>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              {serviceGuides.map((item, index) => (
                <li key={index} style={{ marginBottom: '5px' }}>
                  <Link
                    href={item.link}
                    style={{ color: '#ccc', fontSize: '12px', textDecoration: 'none' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = '#fff'}
                    onMouseLeave={(e) => e.currentTarget.style.color = '#ccc'}
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* 支付方式 */}
          <div>
            <h3 style={{ color: '#fff', fontSize: '14px', fontWeight: 'bold', marginBottom: '10px' }}>支付方式</h3>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              {paymentMethods.map((item, index) => (
                <li key={index} style={{ marginBottom: '5px' }}>
                  <Link
                    href={item.link}
                    style={{ color: '#ccc', fontSize: '12px', textDecoration: 'none' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = '#fff'}
                    onMouseLeave={(e) => e.currentTarget.style.color = '#ccc'}
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* 配送方式 */}
          <div>
            <h3 style={{ color: '#fff', fontSize: '14px', fontWeight: 'bold', marginBottom: '10px' }}>配送方式</h3>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              {deliveryMethods.map((item, index) => (
                <li key={index} style={{ marginBottom: '5px' }}>
                  <Link
                    href={item.link}
                    style={{ color: '#ccc', fontSize: '12px', textDecoration: 'none' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = '#fff'}
                    onMouseLeave={(e) => e.currentTarget.style.color = '#ccc'}
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* 售后服务 */}
          <div>
            <h3 style={{ color: '#fff', fontSize: '14px', fontWeight: 'bold', marginBottom: '10px' }}>售后服务</h3>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              {afterSales.map((item, index) => (
                <li key={index} style={{ marginBottom: '5px' }}>
                  <Link
                    href={item.link}
                    style={{ color: '#ccc', fontSize: '12px', textDecoration: 'none' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = '#fff'}
                    onMouseLeave={(e) => e.currentTarget.style.color = '#ccc'}
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* 关于艺创 */}
          <div>
            <h3 style={{ color: '#fff', fontSize: '14px', fontWeight: 'bold', marginBottom: '10px' }}>关于艺创</h3>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              {aboutUs.map((item, index) => (
                <li key={index} style={{ marginBottom: '5px' }}>
                  <Link
                    href={item.link}
                    style={{ color: '#ccc', fontSize: '12px', textDecoration: 'none' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = '#fff'}
                    onMouseLeave={(e) => e.currentTarget.style.color = '#ccc'}
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* 分隔线 */}
        <div style={{ height: '1px', backgroundColor: '#555', margin: '20px 0' }}></div>

        {/* 底部版权信息 */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', fontSize: '12px' }}>
          <div>
            <p style={{ margin: 0, marginBottom: '5px' }}>
              版权所有 © 2024 艺创包装 |
              <Link
                href="http://www.beian.miit.gov.cn"
                style={{ color: '#ccc', textDecoration: 'none', marginLeft: '5px' }}
                onMouseEnter={(e) => e.currentTarget.style.color = '#fff'}
                onMouseLeave={(e) => e.currentTarget.style.color = '#ccc'}
              >
                豫ICP备20017381号-1
              </Link>
            </p>
            <p style={{ margin: 0, color: '#999' }}>
              本网站LOGO和图片已经申请保护，不经授权不得使用
            </p>
          </div>
          <div>
            <p style={{ margin: 0, marginBottom: '5px' }}>
              有任何问题请联系我们在线客服
            </p>
            <div>
              <Link
                href="/admin"
                style={{ color: '#ccc', textDecoration: 'none', marginRight: '10px' }}
                onMouseEnter={(e) => e.currentTarget.style.color = '#fff'}
                onMouseLeave={(e) => e.currentTarget.style.color = '#ccc'}
              >
                后台登录
              </Link>
              <span style={{ color: '#666' }}>|</span>
              <Link
                href="/front/sitemap"
                style={{ color: '#ccc', textDecoration: 'none', marginLeft: '10px' }}
                onMouseEnter={(e) => e.currentTarget.style.color = '#fff'}
                onMouseLeave={(e) => e.currentTarget.style.color = '#ccc'}
              >
                网站地图
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
