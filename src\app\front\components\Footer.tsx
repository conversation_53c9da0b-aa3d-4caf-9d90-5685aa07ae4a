'use client';

import React from 'react';
import { Layout, Row, Col, Divider } from 'antd';
import { 
  PhoneOutlined, 
  MailOutlined, 
  EnvironmentOutlined,
  QqOutlined,
  WechatOutlined,
} from '@ant-design/icons';
import Link from 'next/link';

const { Footer: AntFooter } = Layout;

/**
 * 页脚组件
 */
export default function Footer() {
  // 服务指南链接
  const serviceGuides = [
    { name: '服务条款', link: '/front/service/terms' },
    { name: '工艺介绍', link: '/front/service/process' },
    { name: '货期及验货标准', link: '/front/service/delivery' },
    { name: '下单注意事项', link: '/front/service/order-notes' },
    { name: '常见问题', link: '/front/service/faq' },
  ];

  // 支付方式链接
  const paymentMethods = [
    { name: '在线支付', link: '/front/payment/online' },
    { name: '银行汇款', link: '/front/payment/bank' },
    { name: '发票说明', link: '/front/payment/invoice' },
  ];

  // 配送方式链接
  const deliveryMethods = [
    { name: '配送范围及时间', link: '/front/delivery/scope' },
    { name: '产品签收与验货', link: '/front/delivery/inspection' },
    { name: '配送费用', link: '/front/delivery/cost' },
  ];

  // 售后服务链接
  const afterSales = [
    { name: '售后服务说明', link: '/front/service/after-sales' },
    { name: '返工退换货', link: '/front/service/return' },
    { name: '退款说明', link: '/front/service/refund' },
  ];

  // 关于我们链接
  const aboutUs = [
    { name: '公司简介', link: '/front/about/company' },
    { name: '人才招聘', link: '/front/about/careers' },
    { name: '联系我们', link: '/front/about/contact' },
  ];

  return (
    <AntFooter className="bg-gray-900 text-gray-300">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 主要内容区域 */}
        <Row gutter={[32, 32]} className="py-12">
          {/* 买家指南 */}
          <Col xs={24} sm={12} md={6} lg={4}>
            <h3 className="text-white font-semibold mb-4 text-lg">买家指南</h3>
            <ul className="space-y-2">
              {serviceGuides.map((item, index) => (
                <li key={index}>
                  <Link 
                    href={item.link}
                    className="text-gray-400 hover:text-white transition-colors duration-300"
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </Col>

          {/* 支付方式 */}
          <Col xs={24} sm={12} md={6} lg={4}>
            <h3 className="text-white font-semibold mb-4 text-lg">支付方式</h3>
            <ul className="space-y-2">
              {paymentMethods.map((item, index) => (
                <li key={index}>
                  <Link 
                    href={item.link}
                    className="text-gray-400 hover:text-white transition-colors duration-300"
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </Col>

          {/* 配送方式 */}
          <Col xs={24} sm={12} md={6} lg={4}>
            <h3 className="text-white font-semibold mb-4 text-lg">配送方式</h3>
            <ul className="space-y-2">
              {deliveryMethods.map((item, index) => (
                <li key={index}>
                  <Link 
                    href={item.link}
                    className="text-gray-400 hover:text-white transition-colors duration-300"
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </Col>

          {/* 售后服务 */}
          <Col xs={24} sm={12} md={6} lg={4}>
            <h3 className="text-white font-semibold mb-4 text-lg">售后服务</h3>
            <ul className="space-y-2">
              {afterSales.map((item, index) => (
                <li key={index}>
                  <Link 
                    href={item.link}
                    className="text-gray-400 hover:text-white transition-colors duration-300"
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </Col>

          {/* 关于艺创 */}
          <Col xs={24} sm={12} md={6} lg={4}>
            <h3 className="text-white font-semibold mb-4 text-lg">关于艺创</h3>
            <ul className="space-y-2">
              {aboutUs.map((item, index) => (
                <li key={index}>
                  <Link 
                    href={item.link}
                    className="text-gray-400 hover:text-white transition-colors duration-300"
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </Col>

          {/* 联系信息 */}
          <Col xs={24} sm={12} md={6} lg={4}>
            <h3 className="text-white font-semibold mb-4 text-lg">联系我们</h3>
            <div className="space-y-3">
              <div className="flex items-center">
                <PhoneOutlined className="mr-2 text-blue-400" />
                <span className="text-sm">18638728164</span>
              </div>
              <div className="flex items-center">
                <QqOutlined className="mr-2 text-blue-400" />
                <span className="text-sm">392548733</span>
              </div>
              <div className="flex items-center">
                <MailOutlined className="mr-2 text-blue-400" />
                <span className="text-sm"><EMAIL></span>
              </div>
              <div className="flex items-start">
                <EnvironmentOutlined className="mr-2 text-blue-400 mt-1" />
                <span className="text-sm">河南省郑州市</span>
              </div>
              <div className="text-sm text-gray-400">
                工作时间：周一至周日 8:30-19:30
              </div>
            </div>
          </Col>
        </Row>

        <Divider className="border-gray-700" />

        {/* 底部版权信息 */}
        <div className="py-6">
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} md={12}>
              <div className="text-center md:text-left">
                <p className="text-gray-400 text-sm mb-2">
                  版权所有 © 2024 艺创包装 | 
                  <Link href="http://www.beian.miit.gov.cn" className="text-gray-400 hover:text-white ml-1">
                    豫ICP备20017381号-1
                  </Link>
                </p>
                <p className="text-gray-500 text-xs">
                  本网站LOGO和图片已经申请保护，不经授权不得使用
                </p>
              </div>
            </Col>
            <Col xs={24} md={12}>
              <div className="text-center md:text-right">
                <p className="text-gray-400 text-sm mb-2">
                  有任何问题请联系我们在线客服
                </p>
                <div className="flex justify-center md:justify-end space-x-4">
                  <Link href="/admin" className="text-gray-400 hover:text-white text-sm">
                    后台登录
                  </Link>
                  <span className="text-gray-600">|</span>
                  <Link href="/front/sitemap" className="text-gray-400 hover:text-white text-sm">
                    网站地图
                  </Link>
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </div>
    </AntFooter>
  );
}
