'use client';

import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Drawer, Space, Dropdown } from 'antd';
import {
  MenuOutlined,
  PhoneOutlined,
  UserOutlined,
  ShoppingCartOutlined,
  LoginOutlined,
  HomeOutlined,
  CalculatorOutlined,
  ShopOutlined,
  InfoCircleOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

const { Header: AntHeader } = Layout;

/**
 * 前台页面头部组件
 */
export default function Header() {
  const pathname = usePathname();
  const [isMobile, setIsMobile] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // 主导航菜单项
  const mainMenuItems = [
    {
      key: 'home',
      icon: <HomeOutlined />,
      label: <Link href="/front">首页</Link>,
    },
    {
      key: 'quote',
      icon: <CalculatorOutlined />,
      label: <Link href="/front/quote">包装报价</Link>,
    },
    {
      key: 'products',
      icon: <ShopOutlined />,
      label: <Link href="/front/products">现货专区</Link>,
    },
    {
      key: 'user',
      icon: <UserOutlined />,
      label: <Link href="/front/user">用户中心</Link>,
    },
    {
      key: 'about',
      icon: <InfoCircleOutlined />,
      label: <Link href="/front/about">关于我们</Link>,
    },
    {
      key: 'news',
      icon: <FileTextOutlined />,
      label: <Link href="/front/news">新闻中心</Link>,
    },
  ];

  // 用户操作菜单
  const userMenuItems = [
    {
      key: 'login',
      label: '登录',
      icon: <LoginOutlined />,
    },
    {
      key: 'orders',
      label: '我的订单',
      icon: <FileTextOutlined />,
    },
    {
      key: 'cart',
      label: '购物车',
      icon: <ShoppingCartOutlined />,
    },
  ];

  // 获取当前选中的菜单键
  const getSelectedKeys = () => {
    if (pathname === '/front') return ['home'];
    if (pathname.startsWith('/front/quote')) return ['quote'];
    if (pathname.startsWith('/front/products')) return ['products'];
    if (pathname.startsWith('/front/user')) return ['user'];
    if (pathname.startsWith('/front/about')) return ['about'];
    if (pathname.startsWith('/front/news')) return ['news'];
    return [];
  };

  return (
    <>
      {/* 顶部信息栏 */}
      <div className="bg-gray-100 border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-10 text-sm">
            <div className="flex items-center space-x-4">
              <span className="flex items-center text-red-600">
                <PhoneOutlined className="mr-1" />
                24小时热线：18638728164
              </span>
            </div>
            <div className="hidden md:flex items-center space-x-4">
              <Link href="/front/login" className="text-gray-600 hover:text-blue-600">
                您好，请登录
              </Link>
              <Link href="/front/user/orders" className="text-gray-600 hover:text-blue-600">
                我的订单
              </Link>
              <Link href="/front/cart" className="text-gray-600 hover:text-blue-600">
                购物车
              </Link>
              <Link href="/front/about" className="text-gray-600 hover:text-blue-600">
                关于我们
              </Link>
              <span className="text-gray-600">帮助中心</span>
            </div>
          </div>
        </div>
      </div>

      {/* 主导航栏 */}
      <AntHeader className="bg-white shadow-sm border-b border-gray-200 px-0">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex items-center">
              <Link href="/front" className="flex items-center">
                <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-lg mr-3">
                  艺
                </div>
                <span className="text-xl font-bold text-gray-900 hidden sm:block">
                  艺创包装
                </span>
              </Link>
            </div>

            {/* 桌面端导航菜单 */}
            {!isMobile && (
              <div className="flex-1 flex justify-center">
                <Menu
                  mode="horizontal"
                  selectedKeys={getSelectedKeys()}
                  items={mainMenuItems}
                  className="border-none bg-transparent"
                  style={{ minWidth: 0, flex: 'auto', justifyContent: 'center' }}
                />
              </div>
            )}

            {/* 右侧操作区 */}
            <div className="flex items-center space-x-4">
              {!isMobile && (
                <Dropdown
                  menu={{ items: userMenuItems }}
                  placement="bottomRight"
                  trigger={['hover']}
                >
                  <Button type="text" icon={<UserOutlined />}>
                    用户中心
                  </Button>
                </Dropdown>
              )}
              
              {/* 移动端菜单按钮 */}
              {isMobile && (
                <Button
                  type="text"
                  icon={<MenuOutlined />}
                  onClick={() => setDrawerVisible(true)}
                  className="text-lg"
                />
              )}
            </div>
          </div>
        </div>
      </AntHeader>

      {/* 移动端抽屉菜单 */}
      <Drawer
        title="菜单"
        placement="right"
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
        width={280}
      >
        <Menu
          mode="vertical"
          selectedKeys={getSelectedKeys()}
          items={[...mainMenuItems, ...userMenuItems]}
          onClick={() => setDrawerVisible(false)}
        />
      </Drawer>
    </>
  );
}
