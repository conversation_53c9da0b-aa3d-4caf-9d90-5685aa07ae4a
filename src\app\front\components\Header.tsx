'use client';

import React, { useState } from 'react';
import { Layout, Menu, Input, Button } from 'antd';
import {
  SearchOutlined,
  PhoneOutlined,
} from '@ant-design/icons';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

const { Header: AntHeader } = Layout;
const { Search } = Input;

/**
 * 前台页面头部组件 - 仿照参考网站设计
 */
export default function Header() {
  const pathname = usePathname();
  const [searchValue, setSearchValue] = useState('');

  // 主导航菜单项
  const mainMenuItems = [
    {
      key: 'home',
      label: <Link href="/front">首页</Link>,
    },
    {
      key: 'quote',
      label: <Link href="/front/quote">包装报价</Link>,
    },
    {
      key: 'products',
      label: <Link href="/front/products">现货专区</Link>,
    },
    {
      key: 'user',
      label: <Link href="/front/user">用户中心</Link>,
    },
    {
      key: 'about',
      label: <Link href="/front/about">关于我们</Link>,
    },
    {
      key: 'news',
      label: <Link href="/front/news">新闻中心</Link>,
    },
  ];

  // 获取当前选中的菜单键
  const getSelectedKeys = () => {
    if (pathname === '/front') return ['home'];
    if (pathname.startsWith('/front/quote')) return ['quote'];
    if (pathname.startsWith('/front/products')) return ['products'];
    if (pathname.startsWith('/front/user')) return ['user'];
    if (pathname.startsWith('/front/about')) return ['about'];
    if (pathname.startsWith('/front/news')) return ['news'];
    return [];
  };

  const handleSearch = (value: string) => {
    console.log('搜索:', value);
    // 这里可以添加搜索逻辑
  };

  return (
    <>
      {/* 顶部信息栏 */}
      <div style={{ backgroundColor: '#f5f5f5', borderBottom: '1px solid #e0e0e0' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 20px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', height: '32px', fontSize: '12px' }}>
            <div>
              <span style={{ color: '#ff4d4f' }}>
                24小时热线：18638728164
              </span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
              <Link href="/front/login" style={{ color: '#666', textDecoration: 'none' }}>
                您好，请登录
              </Link>
              <Link href="/front/user/orders" style={{ color: '#666', textDecoration: 'none' }}>
                我的订单
              </Link>
              <Link href="/front/cart" style={{ color: '#666', textDecoration: 'none' }}>
                购物车
              </Link>
              <Link href="/front/about" style={{ color: '#666', textDecoration: 'none' }}>
                关于我们
              </Link>
              <span style={{ color: '#666' }}>帮助中心</span>
            </div>
          </div>
        </div>
      </div>

      {/* 主导航栏 */}
      <div style={{ backgroundColor: '#fff', borderBottom: '1px solid #e0e0e0' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 20px' }}>
          <div style={{ display: 'flex', alignItems: 'center', height: '80px', gap: '30px' }}>
            {/* Logo */}
            <div>
              <Link href="/front" style={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}>
                <img
                  src="/images/logo.jpg"
                  alt="艺创包装"
                  style={{ height: '50px', width: 'auto' }}
                  onError={(e) => {
                    // 如果图片加载失败，显示文字logo
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    target.nextElementSibling!.style.display = 'block';
                  }}
                />
                <div
                  style={{
                    display: 'none',
                    fontSize: '24px',
                    fontWeight: 'bold',
                    color: '#1890ff'
                  }}
                >
                  艺创包装
                </div>
              </Link>
            </div>

            {/* 搜索框 */}
            <div style={{ flex: 1, maxWidth: '400px' }}>
              <Search
                placeholder="输入关键字"
                allowClear
                enterButton={<SearchOutlined />}
                size="large"
                onSearch={handleSearch}
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
              />
            </div>

            {/* 主导航菜单 */}
            <div>
              <Menu
                mode="horizontal"
                selectedKeys={getSelectedKeys()}
                items={mainMenuItems}
                style={{
                  border: 'none',
                  backgroundColor: 'transparent',
                  fontSize: '14px'
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
