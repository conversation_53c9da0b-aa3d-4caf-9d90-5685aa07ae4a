'use client';

import React from 'react';
import { Carousel } from 'antd';

/**
 * 首页轮播图组件 - 仿照参考网站设计
 */
export default function Hero() {
  // 轮播图数据 - 简化版本
  const carouselItems = [
    {
      id: 1,
      image: '/images/hero/image_1.png',
      alt: '轮播图1',
    },
    {
      id: 2,
      image: '/images/hero/image_2.png',
      alt: '轮播图2',
    },
    {
      id: 3,
      image: '/images/hero/image_3.png',
      alt: '轮播图3',
    },
  ];

  return (
    <div style={{ marginBottom: '10px' }}>
      <Carousel
        autoplay
        autoplaySpeed={4000}
        dots={true}
        style={{ backgroundColor: '#fff', border: '1px solid #e0e0e0' }}
      >
        {carouselItems.map((item) => (
          <div key={item.id}>
            <div style={{ height: '200px', overflow: 'hidden', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <img
                src={item.image}
                alt={item.alt}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                  display: 'block'
                }}
                onError={(e) => {
                  // 如果图片加载失败，显示占位符
                  const target = e.target as HTMLImageElement;
                  target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIiBmaWxsPSIjOTk5Ij7ova/mkq3lm748L3RleHQ+PC9zdmc+';
                }}
              />
            </div>
          </div>
        ))}
      </Carousel>
    </div>
  );
}
