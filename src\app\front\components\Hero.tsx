'use client';

import React from 'react';
import { Carousel, Button } from 'antd';
import { ArrowRightOutlined } from '@ant-design/icons';
import Link from 'next/link';

/**
 * 首页轮播图组件
 */
export default function Hero() {
  // 轮播图数据
  const carouselItems = [
    {
      id: 1,
      title: '专业包装定制服务',
      subtitle: '为您提供一站式包装解决方案',
      description: '从设计到生产，我们为您提供专业的包装定制服务，满足各种行业需求',
      buttonText: '立即报价',
      buttonLink: '/front/quote',
      backgroundImage: '/images/hero/slide-1.jpg',
      backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    },
    {
      id: 2,
      title: '丰富的盒型选择',
      subtitle: '多种样式，满足不同需求',
      description: '屋脊样式、飞机盒、手提袋等多种盒型，专业设计团队为您量身定制',
      buttonText: '查看产品',
      buttonLink: '/front/products',
      backgroundImage: '/images/hero/slide-2.jpg',
      backgroundColor: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    },
    {
      id: 3,
      title: '现货产品快速发货',
      subtitle: '库存充足，当天发货',
      description: '精选现货产品，质量保证，支持小批量采购，快速满足您的紧急需求',
      buttonText: '现货专区',
      buttonLink: '/front/products',
      backgroundImage: '/images/hero/slide-3.jpg',
      backgroundColor: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    },
  ];

  return (
    <div className="relative">
      <Carousel
        autoplay
        autoplaySpeed={5000}
        dots={{ className: 'custom-dots' }}
        effect="fade"
        className="hero-carousel"
      >
        {carouselItems.map((item) => (
          <div key={item.id}>
            <div
              className="relative h-96 md:h-[500px] lg:h-[600px] flex items-center justify-center overflow-hidden"
              style={{
                background: item.backgroundColor,
              }}
            >
              {/* 背景图片 */}
              {item.backgroundImage && (
                <div
                  className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20"
                  style={{
                    backgroundImage: `url(${item.backgroundImage})`,
                  }}
                />
              )}
              
              {/* 内容区域 */}
              <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
                <div className="max-w-3xl mx-auto">
                  <h1 className="text-3xl md:text-5xl lg:text-6xl font-bold mb-4 leading-tight">
                    {item.title}
                  </h1>
                  <p className="text-lg md:text-xl lg:text-2xl mb-6 opacity-90">
                    {item.subtitle}
                  </p>
                  <p className="text-base md:text-lg mb-8 opacity-80 max-w-2xl mx-auto">
                    {item.description}
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Link href={item.buttonLink}>
                      <Button
                        type="primary"
                        size="large"
                        icon={<ArrowRightOutlined />}
                        className="bg-white text-gray-900 border-white hover:bg-gray-100 hover:border-gray-100 h-12 px-8 text-lg font-medium"
                      >
                        {item.buttonText}
                      </Button>
                    </Link>
                    <Link href="/front/about">
                      <Button
                        size="large"
                        className="border-white text-white hover:bg-white hover:text-gray-900 h-12 px-8 text-lg font-medium"
                      >
                        了解更多
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
              
              {/* 装饰元素 */}
              <div className="absolute inset-0 bg-black bg-opacity-10" />
            </div>
          </div>
        ))}
      </Carousel>
      
      {/* 自定义样式 */}
      <style jsx global>{`
        .hero-carousel .slick-dots {
          bottom: 30px;
        }
        
        .hero-carousel .slick-dots li button {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.5);
        }
        
        .hero-carousel .slick-dots li.slick-active button {
          background: white;
        }
        
        .hero-carousel .slick-dots li button:hover {
          background: rgba(255, 255, 255, 0.8);
        }
      `}</style>
    </div>
  );
}
