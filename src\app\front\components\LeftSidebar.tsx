'use client';

import React from 'react';
import Link from 'next/link';

/**
 * 左侧边栏组件 - 产品分类菜单
 */
export default function LeftSidebar() {
  // 产品分类菜单项
  const categoryItems = [
    { name: '屋脊样式', link: '/front/products/wjys' },
    { name: '大翻盖样式', link: '/front/products/dfgys' },
    { name: '自提样式', link: '/front/products/ztys' },
    { name: '双手提样式', link: '/front/products/sstys' },
    { name: '对口箱', link: '/front/products/dkx' },
    { name: '下插口上对口', link: '/front/products/xcksdkys' },
    { name: '下插口上对插', link: '/front/products/xcksdcys' },
    { name: '下陷样式', link: '/front/products/xxys' },
    { name: '飞机盒', link: '/front/products/fjhys' },
    { name: '天地盖样式', link: '/front/products/tdgys' },
    { name: '上下盖样式', link: '/front/products/sxgys' },
    { name: '手提袋', link: '/front/products/stdys' },
    { name: '手提礼盒', link: '/front/products/stlhys' },
    { name: '抽屉盒样式', link: '/front/products/cthys' },
  ];

  return (
    <div style={{ width: '200px', backgroundColor: '#fff', border: '1px solid #e0e0e0' }}>
      {/* 分类标题 */}
      <div 
        style={{ 
          backgroundColor: '#f5f5f5', 
          padding: '10px 15px', 
          borderBottom: '1px solid #e0e0e0',
          fontSize: '14px',
          fontWeight: 'bold',
          color: '#333'
        }}
      >
        所有分类
      </div>
      
      {/* 分类列表 */}
      <ul style={{ margin: 0, padding: 0, listStyle: 'none' }}>
        {categoryItems.map((item, index) => (
          <li key={index}>
            <Link 
              href={item.link}
              style={{
                display: 'block',
                padding: '8px 15px',
                fontSize: '13px',
                color: '#666',
                textDecoration: 'none',
                borderBottom: '1px solid #f0f0f0',
                transition: 'all 0.2s'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#f5f5f5';
                e.currentTarget.style.color = '#1890ff';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = '#666';
              }}
            >
              {item.name}
            </Link>
          </li>
        ))}
      </ul>
    </div>
  );
}
