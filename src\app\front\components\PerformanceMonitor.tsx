'use client';

import { useEffect } from 'react';

interface PerformanceMonitorProps {
  pageName: string;
}

/**
 * 性能监控组件
 * 监控页面加载性能和用户体验指标
 */
export default function PerformanceMonitor({ pageName }: PerformanceMonitorProps) {
  useEffect(() => {
    // 页面加载完成后监控性能指标
    const monitorPerformance = () => {
      if (typeof window !== 'undefined' && 'performance' in window) {
        // 等待页面完全加载
        setTimeout(() => {
          const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
          
          if (navigation) {
            const metrics = {
              // 页面加载时间
              pageLoadTime: navigation.loadEventEnd - navigation.fetchStart,
              // DNS查询时间
              dnsTime: navigation.domainLookupEnd - navigation.domainLookupStart,
              // TCP连接时间
              tcpTime: navigation.connectEnd - navigation.connectStart,
              // 请求响应时间
              requestTime: navigation.responseEnd - navigation.requestStart,
              // DOM解析时间
              domParseTime: navigation.domContentLoadedEventEnd - navigation.domLoading,
              // 资源加载时间
              resourceLoadTime: navigation.loadEventEnd - navigation.domContentLoadedEventEnd,
              // 首次内容绘制时间
              firstContentfulPaint: 0,
              // 最大内容绘制时间
              largestContentfulPaint: 0,
            };

            // 获取Paint Timing API数据
            const paintEntries = performance.getEntriesByType('paint');
            paintEntries.forEach((entry) => {
              if (entry.name === 'first-contentful-paint') {
                metrics.firstContentfulPaint = entry.startTime;
              }
            });

            // 获取LCP数据
            if ('PerformanceObserver' in window) {
              try {
                const lcpObserver = new PerformanceObserver((list) => {
                  const entries = list.getEntries();
                  const lastEntry = entries[entries.length - 1];
                  if (lastEntry) {
                    metrics.largestContentfulPaint = lastEntry.startTime;
                  }
                });
                lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
              } catch (error) {
                console.warn('LCP monitoring not supported:', error);
              }
            }

            // 在开发环境下输出性能指标
            if (process.env.NODE_ENV === 'development') {
              console.group(`🚀 Performance Metrics - ${pageName}`);
              console.log(`📊 Page Load Time: ${metrics.pageLoadTime.toFixed(2)}ms`);
              console.log(`🌐 DNS Time: ${metrics.dnsTime.toFixed(2)}ms`);
              console.log(`🔗 TCP Time: ${metrics.tcpTime.toFixed(2)}ms`);
              console.log(`📡 Request Time: ${metrics.requestTime.toFixed(2)}ms`);
              console.log(`🏗️ DOM Parse Time: ${metrics.domParseTime.toFixed(2)}ms`);
              console.log(`📦 Resource Load Time: ${metrics.resourceLoadTime.toFixed(2)}ms`);
              if (metrics.firstContentfulPaint > 0) {
                console.log(`🎨 First Contentful Paint: ${metrics.firstContentfulPaint.toFixed(2)}ms`);
              }
              if (metrics.largestContentfulPaint > 0) {
                console.log(`🖼️ Largest Contentful Paint: ${metrics.largestContentfulPaint.toFixed(2)}ms`);
              }
              console.groupEnd();

              // 性能警告
              if (metrics.pageLoadTime > 3000) {
                console.warn(`⚠️ Slow page load detected: ${metrics.pageLoadTime.toFixed(2)}ms`);
              }
              if (metrics.firstContentfulPaint > 1500) {
                console.warn(`⚠️ Slow FCP detected: ${metrics.firstContentfulPaint.toFixed(2)}ms`);
              }
              if (metrics.largestContentfulPaint > 2500) {
                console.warn(`⚠️ Slow LCP detected: ${metrics.largestContentfulPaint.toFixed(2)}ms`);
              }
            }

            // 在生产环境下可以发送到分析服务
            if (process.env.NODE_ENV === 'production') {
              // 这里可以集成Google Analytics、百度统计等
              // sendToAnalytics(pageName, metrics);
            }
          }
        }, 1000);
      }
    };

    // 监控内存使用情况
    const monitorMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        if (process.env.NODE_ENV === 'development') {
          console.group(`💾 Memory Usage - ${pageName}`);
          console.log(`Used: ${(memory.usedJSHeapSize / 1048576).toFixed(2)} MB`);
          console.log(`Total: ${(memory.totalJSHeapSize / 1048576).toFixed(2)} MB`);
          console.log(`Limit: ${(memory.jsHeapSizeLimit / 1048576).toFixed(2)} MB`);
          console.groupEnd();
        }
      }
    };

    // 监控长任务
    const monitorLongTasks = () => {
      if ('PerformanceObserver' in window) {
        try {
          const longTaskObserver = new PerformanceObserver((list) => {
            list.getEntries().forEach((entry) => {
              if (process.env.NODE_ENV === 'development') {
                console.warn(`🐌 Long task detected: ${entry.duration.toFixed(2)}ms`);
              }
            });
          });
          longTaskObserver.observe({ entryTypes: ['longtask'] });
        } catch (error) {
          console.warn('Long task monitoring not supported:', error);
        }
      }
    };

    // 页面可见性变化监控
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // 页面隐藏时的处理
        if (process.env.NODE_ENV === 'development') {
          console.log(`👁️ Page hidden - ${pageName}`);
        }
      } else {
        // 页面显示时的处理
        if (process.env.NODE_ENV === 'development') {
          console.log(`👁️ Page visible - ${pageName}`);
        }
      }
    };

    // 启动监控
    monitorPerformance();
    monitorMemory();
    monitorLongTasks();
    
    // 添加页面可见性监听
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // 清理函数
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [pageName]);

  // 这个组件不渲染任何内容
  return null;
}
