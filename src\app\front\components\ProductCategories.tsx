'use client';

import React from 'react';
import { Card, Row, Col } from 'antd';
import { 
  AppstoreOutlined,
  GiftOutlined,
  ShoppingOutlined,
  InboxOutlined,
  FileImageOutlined,
  StarOutlined,
} from '@ant-design/icons';
import Link from 'next/link';

/**
 * 产品分类导航组件
 */
export default function ProductCategories() {
  // 产品分类数据
  const categories = [
    {
      id: 1,
      name: '屋脊样式',
      icon: <AppstoreOutlined className="text-2xl" />,
      popularity: 23267,
      link: '/front/products/wjys',
      color: '#1890ff',
    },
    {
      id: 2,
      name: '大翻盖样式',
      icon: <InboxOutlined className="text-2xl" />,
      popularity: 16558,
      link: '/front/products/dfgys',
      color: '#52c41a',
    },
    {
      id: 3,
      name: '自提样式',
      icon: <ShoppingOutlined className="text-2xl" />,
      popularity: 3313,
      link: '/front/products/ztys',
      color: '#faad14',
    },
    {
      id: 4,
      name: '飞机盒样式',
      icon: <FileImageOutlined className="text-2xl" />,
      popularity: 4982,
      link: '/front/products/fjhys',
      color: '#722ed1',
    },
    {
      id: 5,
      name: '手提礼盒样式',
      icon: <GiftOutlined className="text-2xl" />,
      popularity: 573,
      link: '/front/products/stlhys',
      color: '#eb2f96',
    },
    {
      id: 6,
      name: '手提袋样式',
      icon: <StarOutlined className="text-2xl" />,
      popularity: 10019,
      link: '/front/products/stdys',
      color: '#f5222d',
    },
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题区域 */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            产品分类
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            多种盒型样式，满足不同行业和场景的包装需求
          </p>
        </div>

        {/* 分类网格 */}
        <Row gutter={[24, 24]}>
          {categories.map((category) => (
            <Col xs={12} sm={8} md={6} lg={4} key={category.id}>
              <Link href={category.link}>
                <Card
                  hoverable
                  className="text-center h-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
                  bodyStyle={{ padding: '24px 16px' }}
                >
                  <div 
                    className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-white"
                    style={{ backgroundColor: category.color }}
                  >
                    {category.icon}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {category.name}
                  </h3>
                  <p className="text-sm text-gray-500">
                    人气 {category.popularity.toLocaleString()}
                  </p>
                </Card>
              </Link>
            </Col>
          ))}
        </Row>

        {/* 更多分类按钮 */}
        <div className="text-center mt-12">
          <Link href="/front/products">
            <button className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-300">
              查看全部分类
            </button>
          </Link>
        </div>
      </div>
    </section>
  );
}
