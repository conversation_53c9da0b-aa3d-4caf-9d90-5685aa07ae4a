'use client';

import React from 'react';
import Link from 'next/link';

/**
 * 右侧边栏组件 - 登录区域、活动专区、消息公告
 */
export default function RightSidebar() {
  return (
    <div style={{ width: '200px' }}>
      {/* 登录注册区域 */}
      <div style={{ backgroundColor: '#fff', border: '1px solid #e0e0e0', marginBottom: '10px' }}>
        <div 
          style={{ 
            backgroundColor: '#f5f5f5', 
            padding: '10px 15px', 
            borderBottom: '1px solid #e0e0e0',
            textAlign: 'center'
          }}
        >
          <img 
            src="/images/logintop.jpg" 
            alt="登录提示" 
            style={{ width: '100%', height: 'auto' }}
            onError={(e) => {
              // 如果图片加载失败，显示默认内容
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
              target.nextElementSibling!.style.display = 'block';
            }}
          />
          <div style={{ display: 'none', padding: '20px 0', fontSize: '14px', color: '#666' }}>
            登录享受更多服务
          </div>
        </div>
        <div style={{ padding: '15px', textAlign: 'center' }}>
          <Link 
            href="/front/login"
            style={{
              display: 'inline-block',
              padding: '6px 20px',
              backgroundColor: '#1890ff',
              color: '#fff',
              textDecoration: 'none',
              borderRadius: '3px',
              fontSize: '13px',
              marginRight: '10px'
            }}
          >
            登录
          </Link>
          <Link 
            href="/front/register"
            style={{
              display: 'inline-block',
              padding: '6px 20px',
              backgroundColor: '#52c41a',
              color: '#fff',
              textDecoration: 'none',
              borderRadius: '3px',
              fontSize: '13px'
            }}
          >
            注册
          </Link>
        </div>
      </div>

      {/* 活动专区 */}
      <div style={{ backgroundColor: '#fff', border: '1px solid #e0e0e0', marginBottom: '10px' }}>
        <div 
          style={{ 
            backgroundColor: '#f5f5f5', 
            padding: '10px 15px', 
            borderBottom: '1px solid #e0e0e0',
            fontSize: '14px',
            fontWeight: 'bold',
            color: '#333'
          }}
        >
          活动专区
        </div>
        <div style={{ padding: '15px', textAlign: 'center', color: '#999', fontSize: '13px' }}>
          暂无
        </div>
      </div>

      {/* 消息公告 */}
      <div style={{ backgroundColor: '#fff', border: '1px solid #e0e0e0' }}>
        <div 
          style={{ 
            backgroundColor: '#f5f5f5', 
            padding: '10px 15px', 
            borderBottom: '1px solid #e0e0e0',
            fontSize: '14px',
            fontWeight: 'bold',
            color: '#333'
          }}
        >
          消息公告
        </div>
        <div style={{ padding: '15px' }}>
          <ul style={{ margin: 0, padding: 0, listStyle: 'none' }}>
            <li style={{ marginBottom: '8px' }}>
              <span 
                style={{ 
                  display: 'inline-block', 
                  width: '6px', 
                  height: '6px', 
                  backgroundColor: '#ff4d4f', 
                  borderRadius: '50%', 
                  marginRight: '8px',
                  verticalAlign: 'middle'
                }}
              ></span>
              <Link 
                href="/front/service/terms"
                style={{
                  fontSize: '13px',
                  color: '#666',
                  textDecoration: 'none'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.color = '#1890ff';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.color = '#666';
                }}
              >
                服务条款
              </Link>
            </li>
          </ul>
        </div>
      </div>

      {/* 分隔线图片 */}
      <div style={{ margin: '10px 0', textAlign: 'center' }}>
        <img 
          src="/images/hr.jpg" 
          alt="分隔线" 
          style={{ width: '100%', height: 'auto' }}
          onError={(e) => {
            // 如果图片加载失败，显示默认分隔线
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
            target.nextElementSibling!.style.display = 'block';
          }}
        />
        <div 
          style={{ 
            display: 'none', 
            height: '1px', 
            backgroundColor: '#e0e0e0', 
            margin: '10px 0' 
          }}
        ></div>
      </div>
    </div>
  );
}
