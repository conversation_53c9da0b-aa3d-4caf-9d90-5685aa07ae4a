'use client';

import React from 'react';
import Link from 'next/link';

/**
 * 现货产品展示组件 - 仿照参考网站设计
 */
export default function StockProducts() {
  // 现货产品数据
  const stockProducts = [
    {
      id: 1,
      name: '标准屋脊盒',
      image: '/images/stock/stock-1.jpg',
      popularity: 1665,
      link: '/front/products/1',
    },
    {
      id: 2,
      name: '精装礼品盒',
      image: '/images/stock/stock-2.jpg',
      popularity: 892,
      link: '/front/products/2',
    },
    {
      id: 3,
      name: '快递包装盒',
      image: '/images/stock/stock-3.jpg',
      popularity: 3421,
      link: '/front/products/3',
    },
    {
      id: 4,
      name: '食品包装盒',
      image: '/images/stock/stock-4.jpg',
      popularity: 1234,
      link: '/front/products/4',
    },
    {
      id: 5,
      name: '化妆品包装盒',
      image: '/images/stock/stock-5.jpg',
      popularity: 567,
      link: '/front/products/5',
    },
    {
      id: 6,
      name: '电子产品包装盒',
      image: '/images/stock/stock-6.jpg',
      popularity: 789,
      link: '/front/products/6',
    },
  ];

  return (
    <div style={{ backgroundColor: '#fff', border: '1px solid #e0e0e0', marginBottom: '10px' }}>
      {/* 标题区域 */}
      <div
        style={{
          backgroundColor: '#f5f5f5',
          padding: '10px 15px',
          borderBottom: '1px solid #e0e0e0',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}
      >
        <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#333' }}>
          精品现货
        </span>
        <Link
          href="/front/products"
          style={{ fontSize: '12px', color: '#1890ff', textDecoration: 'none' }}
        >
          更多+
        </Link>
      </div>

      {/* 产品网格 */}
      <div style={{ padding: '15px' }}>
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(3, 1fr)',
            gap: '10px'
          }}
        >
          {stockProducts.map((product) => (
            <div key={product.id} style={{ textAlign: 'center' }}>
              <Link href={product.link} style={{ textDecoration: 'none' }}>
                <div
                  style={{
                    border: '1px solid #f0f0f0',
                    padding: '10px',
                    backgroundColor: '#fafafa',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = '#1890ff';
                    e.currentTarget.style.backgroundColor = '#f0f8ff';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = '#f0f0f0';
                    e.currentTarget.style.backgroundColor = '#fafafa';
                  }}
                >
                  {/* 产品图片 */}
                  <div style={{ height: '80px', marginBottom: '8px', overflow: 'hidden' }}>
                    <img
                      src={product.image}
                      alt={product.name}
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                        display: 'block'
                      }}
                      onError={(e) => {
                        // 如果图片加载失败，显示占位符
                        const target = e.target as HTMLImageElement;
                        target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjgwIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9IiNkZGQiLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1zaXplPSIxMiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iIGZpbGw9IiM5OTkiPuWbvueJhzwvdGV4dD48L3N2Zz4=';
                      }}
                    />
                  </div>

                  {/* 产品名称 */}
                  <div style={{ fontSize: '12px', color: '#333', marginBottom: '4px' }}>
                    {product.name}
                  </div>

                  {/* 人气 */}
                  <div style={{ fontSize: '11px', color: '#999' }}>
                    人气 {product.popularity.toLocaleString()}
                  </div>
                </div>
              </Link>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
