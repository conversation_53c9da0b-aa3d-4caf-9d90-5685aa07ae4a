'use client';

import React from 'react';
import { Card, Row, Col, Tag, Button } from 'antd';
import { ShoppingCartOutlined, EyeOutlined } from '@ant-design/icons';
import Link from 'next/link';

const { Meta } = Card;

/**
 * 现货产品展示组件
 */
export default function StockProducts() {
  // 现货产品数据
  const stockProducts = [
    {
      id: 1,
      name: '标准屋脊盒',
      image: '/images/stock/stock-1.jpg',
      price: 1.25,
      originalPrice: 1.50,
      popularity: 1665,
      stock: 5000,
      description: '标准尺寸屋脊盒，适用于多种产品包装',
      specifications: '300×200×100mm',
      material: '350g白卡纸',
      minOrder: 100,
      isInStock: true,
      discount: 17,
    },
    {
      id: 2,
      name: '精装礼品盒',
      image: '/images/stock/stock-2.jpg',
      price: 3.80,
      originalPrice: 4.50,
      popularity: 892,
      stock: 2000,
      description: '高档礼品包装盒，适合节日送礼',
      specifications: '250×180×80mm',
      material: '400g特种纸',
      minOrder: 50,
      isInStock: true,
      discount: 16,
    },
    {
      id: 3,
      name: '快递包装盒',
      image: '/images/stock/stock-3.jpg',
      price: 0.85,
      originalPrice: 1.00,
      popularity: 3421,
      stock: 10000,
      description: '电商快递专用包装盒，结实耐用',
      specifications: '200×150×100mm',
      material: '3层瓦楞纸',
      minOrder: 200,
      isInStock: true,
      discount: 15,
    },
    {
      id: 4,
      name: '食品包装盒',
      image: '/images/stock/stock-4.jpg',
      price: 2.20,
      originalPrice: 2.60,
      popularity: 1234,
      stock: 3000,
      description: '食品级包装盒，安全环保',
      specifications: '280×200×60mm',
      material: '食品级白卡纸',
      minOrder: 100,
      isInStock: true,
      discount: 15,
    },
    {
      id: 5,
      name: '化妆品包装盒',
      image: '/images/stock/stock-5.jpg',
      price: 4.50,
      originalPrice: 5.20,
      popularity: 567,
      stock: 1500,
      description: '精美化妆品包装盒，提升产品档次',
      specifications: '150×100×50mm',
      material: '350g艺术纸+UV',
      minOrder: 50,
      isInStock: true,
      discount: 13,
    },
    {
      id: 6,
      name: '电子产品包装盒',
      image: '/images/stock/stock-6.jpg',
      price: 6.80,
      originalPrice: 8.00,
      popularity: 789,
      stock: 800,
      description: '电子产品专用包装盒，防震防护',
      specifications: '350×250×150mm',
      material: '双坑瓦楞+彩印',
      minOrder: 30,
      isInStock: false,
      discount: 15,
    },
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题区域 */}
        <div className="flex justify-between items-center mb-12">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              精品现货
            </h2>
            <p className="text-lg text-gray-600">
              库存充足，当天发货，满足您的紧急需求
            </p>
          </div>
          <Link href="/front/products">
            <button className="hidden md:block bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
              更多+
            </button>
          </Link>
        </div>

        {/* 产品网格 */}
        <Row gutter={[24, 24]}>
          {stockProducts.map((product) => (
            <Col xs={12} sm={8} md={6} lg={4} key={product.id}>
              <Card
                hoverable
                className="h-full"
                cover={
                  <div className="relative overflow-hidden h-48 bg-gray-100">
                    {/* 占位图片区域 */}
                    <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-green-50 to-green-100">
                      <div className="text-center">
                        <div className="w-16 h-16 bg-green-200 rounded-lg mx-auto mb-2 flex items-center justify-center">
                          <span className="text-green-600 text-2xl font-bold">
                            {product.name.charAt(0)}
                          </span>
                        </div>
                        <span className="text-green-600 text-sm">{product.name}</span>
                      </div>
                    </div>
                    
                    {/* 折扣标签 */}
                    {product.discount && (
                      <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-bold">
                        -{product.discount}%
                      </div>
                    )}
                    
                    {/* 库存状态 */}
                    <div className="absolute top-2 right-2">
                      <Tag color={product.isInStock ? 'green' : 'red'}>
                        {product.isInStock ? '现货' : '缺货'}
                      </Tag>
                    </div>
                    
                    {/* 悬浮操作按钮 */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center opacity-0 hover:opacity-100">
                      <div className="flex space-x-2">
                        <button className="w-10 h-10 bg-white rounded-full flex items-center justify-center text-gray-600 hover:text-blue-600 transition-colors">
                          <EyeOutlined />
                        </button>
                        <button 
                          className="w-10 h-10 bg-white rounded-full flex items-center justify-center text-gray-600 hover:text-green-600 transition-colors"
                          disabled={!product.isInStock}
                        >
                          <ShoppingCartOutlined />
                        </button>
                      </div>
                    </div>
                  </div>
                }
              >
                <Meta
                  title={
                    <Link href={`/front/products/${product.id}`}>
                      <span className="text-gray-900 hover:text-blue-600 transition-colors">
                        {product.name}
                      </span>
                    </Link>
                  }
                  description={
                    <div className="space-y-2">
                      <p className="text-gray-600 text-sm">{product.description}</p>
                      <div className="text-xs text-gray-500">
                        <p>规格：{product.specifications}</p>
                        <p>材质：{product.material}</p>
                        <p>起订量：{product.minOrder}个</p>
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <span className="text-red-600 font-bold text-lg">
                            ¥{product.price}
                          </span>
                          {product.originalPrice && (
                            <span className="text-gray-400 text-sm line-through ml-2">
                              ¥{product.originalPrice}
                            </span>
                          )}
                        </div>
                        <span className="text-gray-500 text-xs">
                          人气 {product.popularity}
                        </span>
                      </div>
                      <div className="flex space-x-2 mt-3">
                        <Button 
                          type="primary" 
                          size="small" 
                          icon={<ShoppingCartOutlined />}
                          disabled={!product.isInStock}
                          className="flex-1"
                        >
                          {product.isInStock ? '加入购物车' : '暂时缺货'}
                        </Button>
                      </div>
                    </div>
                  }
                />
              </Card>
            </Col>
          ))}
        </Row>

        {/* 移动端更多按钮 */}
        <div className="text-center mt-8 md:hidden">
          <Link href="/front/products">
            <button className="bg-green-600 text-white px-8 py-3 rounded-lg hover:bg-green-700 transition-colors">
              查看更多现货
            </button>
          </Link>
        </div>
      </div>
    </section>
  );
}
