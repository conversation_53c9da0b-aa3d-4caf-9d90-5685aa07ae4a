'use client';

import React from 'react';
import { Layout } from 'antd';
import Header from './components/Header';
import RightSidebar from './components/RightSidebar';
import Hero from './components/Hero';
import FeaturedProducts from './components/FeaturedProducts';
import StockProducts from './components/StockProducts';
import Footer from './components/Footer';
import './styles.css';

const { Content } = Layout;

/**
 * 前台首页 - 仿照参考网站的三列布局
 */
export default function FrontPage() {
  return (
    <Layout style={{ minHeight: '100vh', backgroundColor: '#f5f5f5' }}>

      <Header />

      {/* 主体内容区域 - 三列布局 */}
      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '10px 20px' }}>
        <div style={{ display: 'flex', gap: '10px' }}>
          {/* 中间内容区域 */}
          <div>
            {/* 轮播图区域 */}
            <Hero />

            {/* 精品定制区域 */}
            {/* <FeaturedProducts /> */}

            {/* 精品现货区域 */}
            {/* <StockProducts /> */}
          </div>

          {/* 右侧边栏 */}
          {/* <RightSidebar /> */}
        </div>
      </div>

      <Footer />
    </Layout>
  );
}
