'use client';

import React from 'react';
import { Layout } from 'antd';
import Header from './components/Header';
import Hero from './components/Hero';
import ProductCategories from './components/ProductCategories';
import FeaturedProducts from './components/FeaturedProducts';
import StockProducts from './components/StockProducts';
import Footer from './components/Footer';
import PerformanceMonitor from './components/PerformanceMonitor';
import './styles.css';

const { Content } = Layout;

/**
 * 前台首页
 */
export default function FrontPage() {
  return (
    <Layout className="min-h-screen">
      {/* 性能监控 */}
      <PerformanceMonitor pageName="首页" />

      <Header />
      <Content>
        {/* 轮播图区域 */}
        <Hero />

        {/* 产品分类导航 */}
        <ProductCategories />

        {/* 精品定制区域 */}
        <FeaturedProducts />

        {/* 精品现货区域 */}
        <StockProducts />
      </Content>
      <Footer />
    </Layout>
  );
}
