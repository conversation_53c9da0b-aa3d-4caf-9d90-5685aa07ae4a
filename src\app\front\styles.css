/* 前台页面专用样式 */

/* 全局样式重置 */
.front-layout {
  min-height: 100vh;
  background: #ffffff;
}

/* 轮播图样式优化 */
.hero-carousel .ant-carousel .slick-slide {
  text-align: center;
  height: 400px;
  line-height: 400px;
  background: #364d79;
  overflow: hidden;
}

.hero-carousel .ant-carousel .slick-slide h3 {
  color: #fff;
}

/* 响应式轮播图高度 */
@media (max-width: 768px) {
  .hero-carousel .ant-carousel .slick-slide {
    height: 300px;
    line-height: 300px;
  }
}

@media (min-width: 1024px) {
  .hero-carousel .ant-carousel .slick-slide {
    height: 500px;
    line-height: 500px;
  }
}

/* 产品卡片悬浮效果 */
.product-card {
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 产品图片容器 */
.product-image-container {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.product-image-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%), 
              linear-gradient(-45deg, rgba(255,255,255,0.1) 25%, transparent 25%), 
              linear-gradient(45deg, transparent 75%, rgba(255,255,255,0.1) 75%), 
              linear-gradient(-45deg, transparent 75%, rgba(255,255,255,0.1) 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  opacity: 0.3;
}

/* 价格标签样式 */
.price-tag {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: bold;
  font-size: 14px;
}

.original-price {
  text-decoration: line-through;
  color: #999;
  font-size: 12px;
}

/* 库存状态标签 */
.stock-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
}

.stock-badge.in-stock {
  background: #52c41a;
  color: white;
}

.stock-badge.out-of-stock {
  background: #ff4d4f;
  color: white;
}

/* 折扣标签 */
.discount-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #ff4d4f;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  z-index: 10;
}

/* 导航栏样式优化 */
.front-header {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  position: sticky;
  top: 0;
  z-index: 1000;
}

/* 顶部信息栏样式 */
.top-info-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 13px;
}

.top-info-bar a {
  color: rgba(255, 255, 255, 0.9);
  transition: color 0.3s ease;
}

.top-info-bar a:hover {
  color: white;
}

/* 页脚样式优化 */
.front-footer {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.front-footer h3 {
  position: relative;
  padding-bottom: 8px;
}

.front-footer h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 30px;
  height: 2px;
  background: #3498db;
}

/* 按钮样式优化 */
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(17, 153, 142, 0.4);
}

/* 分类图标样式 */
.category-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  transition: all 0.3s ease;
}

.category-icon:hover {
  transform: scale(1.1);
}

/* 加载动画 */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 响应式优化 */
@media (max-width: 640px) {
  .hero-section h1 {
    font-size: 2rem;
    line-height: 1.2;
  }
  
  .hero-section p {
    font-size: 1rem;
  }
  
  .product-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .category-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .category-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .product-grid {
    grid-template-columns: 1fr;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 焦点样式优化 */
button:focus,
a:focus,
input:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* 无障碍优化 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
