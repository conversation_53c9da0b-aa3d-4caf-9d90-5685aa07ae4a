@tailwind base;
@tailwind components;
@tailwind utilities;


:root {
  --foreground-rgb: 0, 0, 0;
  --background-rgb: 255, 255, 255;
}

/* 重置body边距和padding */
body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-rgb));
}

/* 确保#__next和页面容器占满整个屏幕 */
#__next {
  width: 100%;
  height: 100%;
}

/* Ant Design 相关覆盖样式 */
.ant-layout {
  min-height: 100vh !important;
  background: rgb(var(--background-rgb));
}

/* 确保Layout组件占满整个屏幕 */
.ant-layout.ant-layout-has-sider {
  min-height: 100vh;
  height: 100%;
}

/* 修复顶部Header的样式 */
.ant-layout-header {
  padding: 0 !important;
  height: 64px !important;
  line-height: 64px !important;
}

/* 修复侧边栏的样式 */
.ant-layout-sider {
  overflow: auto;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
}

/* 确保内容区域不被侧边栏覆盖 */
.ant-layout-has-sider .ant-layout {
  margin-left: 200px; /* 侧边栏展开宽度 */
  transition: margin-left 0.2s;
}

.ant-layout-has-sider .ant-layout-sider-collapsed + .ant-layout {
  margin-left: 80px; /* 侧边栏收起宽度 */
}

/* 卡片样式优化 */
.ant-card {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  border-radius: 2px;
  border: 1px solid #f0f0f0;
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

/* 表格样式优化 */
.ant-table-wrapper {
  background: #fff;
  border-radius: 2px;
}

/* 按钮间距 */
.ant-btn + .ant-btn {
  margin-left: 8px;
}

/* 响应式布局调整 */
@media (max-width: 768px) {
  .ant-layout-has-sider .ant-layout {
    margin-left: 0;
  }
  
  .ant-layout-sider {
    position: absolute;
    z-index: 200;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
    transform: translateX(-100%);
  }
  
  .ant-layout-sider-collapsed {
    transform: translateX(0);
  }
  
  .ant-layout-content {
    margin: 8px !important;
    padding: 16px !important;
  }
}
