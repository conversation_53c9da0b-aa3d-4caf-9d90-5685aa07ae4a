import 'antd/dist/reset.css'; // 样式是必须的

import './globals.css';
import { Inter } from 'next/font/google';
import { Metadata } from 'next';
import { AntdProvider } from './providers';

const inter = Inter({ subsets: ['latin'] });

import { ConfigProvider } from 'antd';



export const metadata: Metadata = {
  title: '纸箱包装智能设计系统',
  description: '纸箱包装智能设计系统',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN">
      <ConfigProvider prefixCls="ant">
        <body className={inter.className}>
          <AntdProvider>{children}</AntdProvider>
        </body>
      </ConfigProvider>
    </html>
  );
} 