import { MetadataRoute } from 'next';

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      {
        userAgent: '*',
        allow: ['/front/', '/front/products/', '/front/about/', '/front/news/'],
        disallow: ['/admin/', '/api/', '/front/user/', '/_next/'],
      },
      {
        userAgent: 'Googlebot',
        allow: ['/front/', '/front/products/', '/front/about/', '/front/news/'],
        disallow: ['/admin/', '/api/', '/front/user/'],
      },
      {
        userAgent: '<PERSON>duspider',
        allow: ['/front/', '/front/products/', '/front/about/', '/front/news/'],
        disallow: ['/admin/', '/api/', '/front/user/'],
      },
    ],
    sitemap: 'https://www.ycbz.com/sitemap.xml',
  };
}
